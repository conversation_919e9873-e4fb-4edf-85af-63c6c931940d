<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图像导航修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .canvas-container {
            width: 600px;
            height: 400px;
            border: 2px solid #ccc;
            border-radius: 4px;
            margin: 20px 0;
            position: relative;
            background: #f8f9fa;
        }
        #test-canvas {
            display: block;
        }
        .controls {
            margin: 10px 0;
        }
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #fff;
            cursor: pointer;
        }
        .controls button:hover {
            background: #f0f0f0;
        }
        .controls button:disabled {
            background: #f5f5f5;
            color: #999;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-entry.success {
            color: #28a745;
        }
        .log-entry.error {
            color: #dc3545;
        }
        .log-entry.warning {
            color: #ffc107;
        }
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .instructions h4 {
            margin-top: 0;
            color: #0066cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 图像导航修复测试</h1>
        <p>此页面用于测试修复后的图像导航功能，验证 Canvas 初始化和事件绑定是否正常工作。</p>

        <div class="instructions">
            <h4>📋 测试说明</h4>
            <ul>
                <li><strong>步骤1:</strong> 点击"初始化 Canvas"按钮</li>
                <li><strong>步骤2:</strong> 等待初始化完成后，点击"绑定导航事件"</li>
                <li><strong>步骤3:</strong> 测试以下功能：
                    <ul>
                        <li>按住 Alt 键拖拽进行平移</li>
                        <li>使用鼠标滚轮进行缩放</li>
                        <li>中键拖拽进行平移</li>
                    </ul>
                </li>
                <li><strong>步骤4:</strong> 观察日志输出，确认没有错误</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🎯 Canvas 测试区域</h3>
            <div id="status" class="status warning">等待初始化...</div>
            
            <div class="controls">
                <button id="init-btn" onclick="initCanvas()">初始化 Canvas</button>
                <button id="bind-btn" onclick="bindEvents()" disabled>绑定导航事件</button>
                <button id="unbind-btn" onclick="unbindEvents()" disabled>解绑事件</button>
                <button id="test-btn" onclick="testCanvas()" disabled>测试 Canvas 状态</button>
                <button id="clear-btn" onclick="clearLog()">清空日志</button>
            </div>

            <div class="canvas-container">
                <canvas id="test-canvas" width="600" height="400"></canvas>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 测试日志</h3>
            <div id="log" class="log">
                <div class="log-entry">等待开始测试...</div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/fabric@5.3.0/dist/fabric.min.js"></script>
    <script>
        let canvas = null;
        let isDragging = false;
        let lastPanPoint = { x: 0, y: 0 };
        let eventHandlers = {
            mouseDown: null,
            mouseMove: null,
            mouseUp: null,
            mouseWheel: null
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateStatus(message, type = 'warning') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function updateButtons(init, bind, unbind, test) {
            document.getElementById('init-btn').disabled = !init;
            document.getElementById('bind-btn').disabled = !bind;
            document.getElementById('unbind-btn').disabled = !unbind;
            document.getElementById('test-btn').disabled = !test;
        }

        async function initCanvas() {
            try {
                log('开始初始化 Fabric Canvas...');
                
                const canvasElement = document.getElementById('test-canvas');
                if (!canvasElement) {
                    throw new Error('Canvas 元素未找到');
                }

                canvas = new fabric.Canvas(canvasElement, {
                    selection: false,
                    preserveObjectStacking: true,
                    renderOnAddRemove: false,
                    skipTargetFind: true,
                    evented: true,
                    backgroundColor: '#f8f9fa'
                });

                // 等待初始化完成
                await new Promise(resolve => setTimeout(resolve, 100));

                // 检查事件系统
                if (typeof canvas.on !== 'function') {
                    throw new Error('Canvas 事件系统未初始化');
                }

                log('Canvas 初始化成功', 'success');
                updateStatus('Canvas 已初始化，可以绑定事件', 'success');
                updateButtons(false, true, false, true);

                // 添加一个测试图形
                const rect = new fabric.Rect({
                    left: 250,
                    top: 150,
                    width: 100,
                    height: 100,
                    fill: '#ff6b6b',
                    selectable: false,
                    evented: false
                });
                canvas.add(rect);
                canvas.renderAll();

                log('添加测试图形完成', 'success');

            } catch (error) {
                log(`Canvas 初始化失败: ${error.message}`, 'error');
                updateStatus('Canvas 初始化失败', 'error');
                updateButtons(true, false, false, false);
            }
        }

        function isCanvasReady() {
            if (!canvas) {
                log('Canvas 对象不存在', 'warning');
                return false;
            }
            
            try {
                if (typeof canvas.on !== 'function' ||
                    typeof canvas.off !== 'function' ||
                    typeof canvas.getElement !== 'function') {
                    log('Canvas 方法不可用', 'warning');
                    return false;
                }

                if (!canvas.viewportTransform || !Array.isArray(canvas.viewportTransform)) {
                    log('Canvas viewportTransform 不可用', 'warning');
                    return false;
                }

                const element = canvas.getElement();
                if (!element || !element.parentNode) {
                    log('Canvas DOM 元素未挂载', 'warning');
                    return false;
                }

                if (typeof canvas.getZoom !== 'function' ||
                    typeof canvas.zoomToPoint !== 'function' ||
                    typeof canvas.setCursor !== 'function') {
                    log('Canvas 缩放/光标方法不可用', 'warning');
                    return false;
                }

                return true;
            } catch (error) {
                log(`Canvas 状态检查失败: ${error.message}`, 'error');
                return false;
            }
        }

        function bindEvents() {
            if (!isCanvasReady()) {
                log('Canvas 未准备好，无法绑定事件', 'error');
                return;
            }

            try {
                // 移除现有事件
                unbindEvents();

                eventHandlers.mouseDown = (opt) => {
                    if (!canvas) return;
                    const evt = opt.e;
                    if (evt.altKey || evt.button === 1) {
                        isDragging = true;
                        canvas.selection = false;
                        lastPanPoint.x = evt.clientX;
                        lastPanPoint.y = evt.clientY;
                        canvas.setCursor('grabbing');
                        log('开始拖拽', 'info');
                    }
                };

                eventHandlers.mouseMove = (opt) => {
                    if (isDragging && canvas) {
                        const evt = opt.e;
                        const vpt = canvas.viewportTransform;
                        if (vpt && Array.isArray(vpt) && vpt.length >= 6) {
                            vpt[4] += evt.clientX - lastPanPoint.x;
                            vpt[5] += evt.clientY - lastPanPoint.y;
                            canvas.requestRenderAll();
                            lastPanPoint.x = evt.clientX;
                            lastPanPoint.y = evt.clientY;
                        }
                    }
                };

                eventHandlers.mouseUp = () => {
                    if (canvas && isDragging) {
                        const vpt = canvas.viewportTransform;
                        if (vpt) {
                            canvas.setViewportTransform(vpt);
                        }
                        isDragging = false;
                        canvas.selection = true;
                        canvas.setCursor('default');
                        log('结束拖拽', 'info');
                    }
                };

                eventHandlers.mouseWheel = (opt) => {
                    if (!canvas) return;
                    const evt = opt.e;
                    const delta = evt.deltaY;
                    let zoom = canvas.getZoom();
                    zoom *= 0.999 ** delta;
                    zoom = Math.max(0.1, Math.min(10, zoom));

                    const point = new fabric.Point(evt.offsetX, evt.offsetY);
                    canvas.zoomToPoint(point, zoom);
                    log(`缩放到: ${(zoom * 100).toFixed(1)}%`, 'info');

                    evt.preventDefault();
                    evt.stopPropagation();
                };

                // 绑定事件
                canvas.on('mouse:down', eventHandlers.mouseDown);
                canvas.on('mouse:move', eventHandlers.mouseMove);
                canvas.on('mouse:up', eventHandlers.mouseUp);
                canvas.on('mouse:wheel', eventHandlers.mouseWheel);

                log('事件绑定成功', 'success');
                updateStatus('事件已绑定，可以测试导航功能', 'success');
                updateButtons(false, false, true, true);

            } catch (error) {
                log(`事件绑定失败: ${error.message}`, 'error');
                updateStatus('事件绑定失败', 'error');
            }
        }

        function unbindEvents() {
            if (!canvas) return;
            
            try {
                if (eventHandlers.mouseDown) {
                    canvas.off('mouse:down', eventHandlers.mouseDown);
                }
                if (eventHandlers.mouseMove) {
                    canvas.off('mouse:move', eventHandlers.mouseMove);
                }
                if (eventHandlers.mouseUp) {
                    canvas.off('mouse:up', eventHandlers.mouseUp);
                }
                if (eventHandlers.mouseWheel) {
                    canvas.off('mouse:wheel', eventHandlers.mouseWheel);
                }
                
                eventHandlers.mouseDown = null;
                eventHandlers.mouseMove = null;
                eventHandlers.mouseUp = null;
                eventHandlers.mouseWheel = null;

                log('事件解绑成功', 'success');
                updateStatus('事件已解绑', 'warning');
                updateButtons(false, true, false, true);

            } catch (error) {
                log(`事件解绑失败: ${error.message}`, 'error');
            }
        }

        function testCanvas() {
            log('=== Canvas 状态测试 ===', 'info');
            
            if (!canvas) {
                log('❌ Canvas 对象不存在', 'error');
                return;
            }

            log('✅ Canvas 对象存在', 'success');
            log(`✅ Canvas 类型: ${canvas.constructor.name}`, 'info');
            log(`✅ Canvas 方法检查:`, 'info');
            log(`  - on: ${typeof canvas.on}`, 'info');
            log(`  - off: ${typeof canvas.off}`, 'info');
            log(`  - getElement: ${typeof canvas.getElement}`, 'info');
            log(`  - getZoom: ${typeof canvas.getZoom}`, 'info');
            log(`  - zoomToPoint: ${typeof canvas.zoomToPoint}`, 'info');
            log(`  - setCursor: ${typeof canvas.setCursor}`, 'info');
            
            const vpt = canvas.viewportTransform;
            log(`✅ viewportTransform: ${Array.isArray(vpt) ? `[${vpt.join(', ')}]` : 'null'}`, 'info');
            
            const element = canvas.getElement();
            log(`✅ DOM 元素: ${element ? '存在' : '不存在'}`, element ? 'success' : 'error');
            log(`✅ 父节点: ${element && element.parentNode ? '存在' : '不存在'}`, element && element.parentNode ? 'success' : 'error');
            
            log(`✅ 当前缩放: ${canvas.getZoom()}`, 'info');
            log('=== 测试完成 ===', 'info');
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div class="log-entry">日志已清空...</div>';
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            log('页面加载完成，准备测试', 'info');
            updateButtons(true, false, false, false);
        });
    </script>
</body>
</html>
