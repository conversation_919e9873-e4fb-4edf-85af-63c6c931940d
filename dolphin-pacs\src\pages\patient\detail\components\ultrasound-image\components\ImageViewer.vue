<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading, Warning, Picture } from '@element-plus/icons-vue'
import { useFabricCanvas } from '../composables/useFabricCanvas'
import { useImageZoom } from '../composables/useImageZoom'
import { useImageNavigation } from '../composables/useImageNavigation'
import type { ImageInfo, LoadingState } from '../types'

interface Props {
  imageInfo: ImageInfo | null
}

interface Emits {
  (e: 'load', imageInfo: ImageInfo): void
  (e: 'error', error: Error): void
  (e: 'zoom-change', zoom: number): void
}

const props = defineProps<Props>()

const emit = defineEmits<Emits>()

// 组件状态
const canvasContainer = ref<HTMLDivElement>()
const loadingState = ref<LoadingState>('idle')
const canvasId = 'ultrasound-canvas'

// 使用组合式函数
const {
  canvas,
  isReady,
  initCanvas,
  loadImage,
  fitToCanvas,
  setZoom: setCanvasZoom,
  getZoom,
  resetView,
  rotateImage,
  resizeCanvas
} = useFabricCanvas(canvasId)

const {
  currentZoom,
  zoomPercentage,
  canZoomIn,
  canZoomOut,
  zoomIn,
  zoomOut,
  resetZoom,
  actualSize,
  handleWheelZoom
} = useImageZoom({ min: 0.1, max: 10, step: 0.25, default: 1 })

// 处理滚轮缩放回调
const handleWheelZoomChange = (zoom: number) => {
  currentZoom.value = zoom
}

const { enablePanning, handleKeyNavigation } = useImageNavigation(canvas, handleWheelZoomChange)

// 监听缩放变化
watch(currentZoom, (newZoom) => {
  setCanvasZoom(newZoom)
  emit('zoom-change', newZoom)
})

// 监听 Canvas 准备状态
watch(isReady, async (ready: boolean) => {
  if (ready && canvas.value) {
    console.log('🎯 Canvas is ready, attempting to enable panning...')
    await enablePanningWithRetry()
  }
})

// 监听图像信息变化
watch(() => props.imageInfo, async (newImageInfo: ImageInfo | null) => {
  if (newImageInfo && isReady.value) {
    await loadImageData(newImageInfo)
  }
}, { immediate: true })

// 加载图像数据
const loadImageData = async (imageInfo: ImageInfo) => {
  try {
    loadingState.value = 'loading'
    await loadImage(imageInfo)
    loadingState.value = 'loaded'
    resetZoom()
    emit('load', imageInfo)
  } catch (error) {
    loadingState.value = 'error'
    const errorObj = error instanceof Error ? error : new Error('图像加载失败')
    ElMessage.error(errorObj.message)
    emit('error', errorObj)
  }
}



// 处理容器大小变化
const handleResize = () => {
  if (canvasContainer.value && canvas.value) {
    const { clientWidth, clientHeight } = canvasContainer.value
    resizeCanvas(clientWidth, clientHeight)
  }
}

// 初始化
onMounted(async () => {
  try {
    console.log('🚀 Starting ImageViewer initialization...')

    await nextTick()

    // 初始化 Canvas
    await initCanvas()

    // 检查 Canvas 是否成功初始化
    if (!isReady.value || !canvas.value) {
      throw new Error('Canvas initialization failed')
    }

    console.log('✅ Canvas initialized successfully')

    // 等待 Canvas 完全初始化
    await new Promise(resolve => setTimeout(resolve, 200))

    // 设置初始画布大小
    if (canvasContainer.value) {
      const { clientWidth, clientHeight } = canvasContainer.value
      resizeCanvas(clientWidth, clientHeight)
    }

    // 再次等待确保画布大小设置完成
    await new Promise(resolve => setTimeout(resolve, 100))

    // 绑定键盘事件
    document.addEventListener('keydown', handleKeyNavigation)

    // 如果有图像信息，立即加载
    if (props.imageInfo) {
      await loadImageData(props.imageInfo)
    }

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)

    console.log('✅ ImageViewer initialized successfully')
  } catch (error) {
    console.error('❌ Failed to initialize ImageViewer:', error)
    ElMessage.error('图像查看器初始化失败')
  }
})

// 带重试的启用平移功能
const enablePanningWithRetry = async (maxRetries = 3, delay = 300) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      if (canvas.value &&
          typeof canvas.value.on === 'function' &&
          typeof canvas.value.off === 'function' &&
          canvas.value.viewportTransform &&
          canvas.value.getElement() &&
          canvas.value.getElement().parentNode) {

        console.log(`📱 Enabling panning functionality... (attempt ${i + 1})`)
        enablePanning()
        console.log('✅ Panning enabled successfully')
        return
      }
    } catch (error) {
      console.warn(`⚠️ Attempt ${i + 1} failed:`, error)
    }

    if (i < maxRetries - 1) {
      console.warn(`⚠️ Canvas not ready for event binding, retrying in ${delay}ms... (${i + 1}/${maxRetries})`)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  console.error('❌ Failed to enable panning after maximum retries')
}

// 清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('keydown', handleKeyNavigation)
})

// 旋转方法
const rotateLeft = () => {
  rotateImage(-90)
}

const rotateRight = () => {
  rotateImage(90)
}

// 暴露方法给父组件
defineExpose({
  fitToCanvas,
  resetView,
  zoomIn,
  zoomOut,
  actualSize,
  getZoom,
  rotateLeft,
  rotateRight
})
</script>

<template>
  <div class="image-viewer">
    <!-- 画布容器 -->
    <div
      ref="canvasContainer"
      class="canvas-container"
    >
      <!-- Fabric.js Canvas -->
      <canvas 
        :id="canvasId"
        class="fabric-canvas"
      />

      <!-- 加载状态 -->
      <div v-if="loadingState === 'loading'" class="loading-overlay">
        <el-icon class="loading-icon is-loading"><Loading /></el-icon>
        <span class="loading-text">正在加载图像...</span>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="loadingState === 'error'" class="error-overlay">
        <el-icon class="error-icon"><Warning /></el-icon>
        <span class="error-text">图像加载失败</span>
      </div>

      <!-- 无图像状态 -->
      <div v-else-if="!imageInfo" class="empty-overlay">
        <el-icon class="empty-icon"><Picture /></el-icon>
        <span class="empty-text">请选择要查看的超声图像</span>
      </div>
    </div>

    <!-- 操作提示 -->
    <div class="operation-tips">
      <span class="tip-text">
        💡 提示：按住Alt键拖拽可平移图像，滚轮可缩放
      </span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.image-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0; /* 允许收缩 */

  .canvas-container {
    position: relative;
    flex: 1;
    width: 100%;
    height: 100%;
    min-height: 200px; /* 减少最小高度限制，提供更好的自适应性 */
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
    overflow: hidden;
    background: #f8f9fa;

    .fabric-canvas {
      display: block;
    }

    .loading-overlay,
    .error-overlay,
    .empty-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(2px);
    }

    .loading-overlay {
      .loading-text {
        margin-top: 12px;
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }

    .error-overlay {
      .error-icon {
        font-size: 48px;
        color: var(--el-color-danger);
        margin-bottom: 12px;
      }

      .error-text {
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }

    .empty-overlay {
      .empty-icon {
        font-size: 48px;
        color: var(--el-text-color-placeholder);
        margin-bottom: 12px;
      }

      .empty-text {
        font-size: 14px;
        color: var(--el-text-color-placeholder);
      }
    }
  }

  .operation-tips {
    padding: 8px 12px;
    background: var(--el-bg-color-page);
    border-radius: 4px;
    margin-top: 8px;

    .tip-text {
      font-size: 12px;
      color: var(--el-text-color-regular);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .image-viewer {
    .operation-tips {
      padding: 6px 8px;

      .tip-text {
        font-size: 11px;
      }
    }
  }
}
</style>
