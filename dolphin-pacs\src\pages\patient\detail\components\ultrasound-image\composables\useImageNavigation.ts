import { ref, onUnmounted, readonly, type Ref } from 'vue'
import * as fabric from 'fabric'
import type { IEvent } from 'fabric/fabric-impl'

export function useImageNavigation(canvas: Ref<fabric.Canvas | null>, onZoomChange?: (zoom: number) => void) {
  const isDragging = ref(false)
  const lastPanPoint = ref({ x: 0, y: 0 })
  let retryCount = 0
  const maxRetries = 5
  let retryTimeout: number | null = null

  const isCanvasReady = (): boolean => {
    if (!canvas.value) return false
    try {
      if (typeof canvas.value.on !== 'function' ||
          typeof canvas.value.off !== 'function' ||
          typeof canvas.value.getElement !== 'function' ||
          !canvas.value.viewportTransform) {
        return false
      }
      const element = canvas.value.getElement()
      return !!element && !!element.parentNode
    } catch (error) {
      console.warn('Canvas readiness check failed:', error)
      return false
    }
  }

  const eventHandlers = {
    mouseDown: null as ((opt: IEvent) => void) | null,
    mouseMove: null as ((opt: IEvent) => void) | null,
    mouseUp: null as (() => void) | null,
    mouseWheel: null as ((opt: IEvent) => void) | null
  }

  const enablePanning = () => {
    if (!isCanvasReady()) {
      if (retryCount < maxRetries) {
        retryCount++
        console.warn(`Canvas not ready for event binding, retrying... (${retryCount}/${maxRetries})`)
        retryTimeout = setTimeout(() => enablePanning(), 300) as any
        return
      } else {
        console.error('Canvas failed to become ready after maximum retries')
        return
      }
    }

    // 移除现有事件监听器
    disablePanning()

    try {
      eventHandlers.mouseDown = (opt: IEvent) => {
        const evt = opt.e as MouseEvent
        if (evt.altKey || evt.button === 1) {
          isDragging.value = true
          canvas.value!.selection = false
          lastPanPoint.value.x = evt.clientX
          lastPanPoint.value.y = evt.clientY
          canvas.value!.setCursor('grabbing')
        }
      }

      eventHandlers.mouseMove = (opt: IEvent) => {
        if (isDragging.value && canvas.value) {
          const evt = opt.e as MouseEvent
          const vpt = canvas.value.viewportTransform!
          vpt[4] += evt.clientX - lastPanPoint.value.x
          vpt[5] += evt.clientY - lastPanPoint.value.y
          canvas.value.requestRenderAll()
          lastPanPoint.value.x = evt.clientX
          lastPanPoint.value.y = evt.clientY
        }
      }

      eventHandlers.mouseUp = () => {
        if (canvas.value) {
          canvas.value.setViewportTransform(canvas.value.viewportTransform!)
          isDragging.value = false
          canvas.value.selection = true
          canvas.value.setCursor('default')
        }
      }

      eventHandlers.mouseWheel = (opt: IEvent) => {
        const evt = opt.e as WheelEvent
        const delta = evt.deltaY
        let zoom = canvas.value!.getZoom()
        zoom *= 0.999 ** delta
        zoom = Math.max(0.1, Math.min(10, zoom))

        const point = new fabric.Point(evt.offsetX, evt.offsetY)
        canvas.value!.zoomToPoint(point, zoom)

        if (onZoomChange) onZoomChange(zoom)
        evt.preventDefault()
        evt.stopPropagation()
      }

      canvas.value.on('mouse:down', eventHandlers.mouseDown!)
      canvas.value.on('mouse:move', eventHandlers.mouseMove!)
      canvas.value.on('mouse:up', eventHandlers.mouseUp!)
      canvas.value.on('mouse:wheel', eventHandlers.mouseWheel!)
      retryCount = 0
      console.log('✅ Image navigation events bound successfully')
    } catch (error) {
      console.error('❌ Failed to bind image navigation events:', error)
    }
  }

  const disablePanning = () => {
    if (!canvas.value) return
    if (retryTimeout) clearTimeout(retryTimeout)
    try {
      canvas.value.off('mouse:down', eventHandlers.mouseDown!)
      canvas.value.off('mouse:move', eventHandlers.mouseMove!)
      canvas.value.off('mouse:up', eventHandlers.mouseUp!)
      canvas.value.off('mouse:wheel', eventHandlers.mouseWheel!)
      eventHandlers.mouseDown = eventHandlers.mouseMove = eventHandlers.mouseUp = eventHandlers.mouseWheel = null
    } catch (error) {
      console.warn('Failed to remove some event listeners:', error)
    }
  }

  const resetViewport = () => {
    if (!canvas.value) return
    canvas.value.setViewportTransform([1, 0, 0, 1, 0, 0])
    canvas.value.setZoom(1)
    canvas.value.requestRenderAll()
  }

  const centerView = () => {
    if (!canvas.value) return
    const objects = canvas.value.getObjects()
    if (objects.length === 0) return
    const group = new fabric.Group(objects, { left: 0, top: 0 })
    const { width, height } = group
    const { width: canvasWidth, height: canvasHeight } = canvas.value
    canvas.value.setViewportTransform([1, 0, 0, 1, (canvasWidth - width!) / 2, (canvasHeight - height!) / 2])
    canvas.value.requestRenderAll()
  }

  const panTo = (x: number, y: number) => {
    if (!canvas.value) return
    const vpt = canvas.value.viewportTransform!
    vpt[4] = x
    vpt[5] = y
    canvas.value.setViewportTransform(vpt)
    canvas.value.requestRenderAll()
  }

  const getPanPosition = () => {
    if (!canvas.value) return { x: 0, y: 0 }
    const vpt = canvas.value.viewportTransform!
    return { x: vpt[4], y: vpt[5] }
  }

  const handleKeyNavigation = (event: KeyboardEvent) => {
    if (!canvas.value || !canvas.value.getActiveObject()) return
    const step = 10
    const vpt = canvas.value.viewportTransform!
    switch (event.key) {
      case 'ArrowUp': vpt[5] += step; break
      case 'ArrowDown': vpt[5] -= step; break
      case 'ArrowLeft': vpt[4] += step; break
      case 'ArrowRight': vpt[4] -= step; break
      default: return
    }
    event.preventDefault()
    canvas.value.setViewportTransform(vpt)
    canvas.value.requestRenderAll()
  }

  const bindKeyboardEvents = () => document.addEventListener('keydown', handleKeyNavigation)
  const unbindKeyboardEvents = () => document.removeEventListener('keydown', handleKeyNavigation)

  onUnmounted(() => {
    disablePanning()
    unbindKeyboardEvents()
  })

  return {
    isDragging: readonly(isDragging),
    enablePanning,
    disablePanning,
    resetViewport,
    centerView,
    panTo,
    getPanPosition,
    handleKeyNavigation
  }
}