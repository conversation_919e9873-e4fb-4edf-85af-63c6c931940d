import { ref, onUnmounted, readonly, type Ref } from 'vue'
import * as fabric from 'fabric'

export function useImageNavigation(canvas: Ref<fabric.Canvas | null>, onZoomChange?: (zoom: number) => void) {
  const isDragging = ref(false)
  const lastPanPoint = ref({ x: 0, y: 0 })
  let retryCount = 0
  const maxRetries = 5
  let retryTimeout: number | null = null
  const isEventsBound = ref(false)

  const isCanvasReady = (): boolean => {
    if (!canvas.value) {
      console.warn('Canvas value is null or undefined')
      return false
    }

    try {
      // 检查 Canvas 对象的基本方法
      if (typeof canvas.value.on !== 'function' ||
          typeof canvas.value.off !== 'function' ||
          typeof canvas.value.getElement !== 'function') {
        console.warn('Canvas methods not available')
        return false
      }

      // 检查 viewportTransform 是否存在
      if (!canvas.value.viewportTransform || !Array.isArray(canvas.value.viewportTransform)) {
        console.warn('Canvas viewportTransform not available')
        return false
      }

      // 检查 DOM 元素是否存在且已挂载
      const element = canvas.value.getElement()
      if (!element || !element.parentNode) {
        console.warn('Canvas DOM element not mounted')
        return false
      }

      // 检查 Canvas 是否已完全初始化
      if (typeof canvas.value.getZoom !== 'function' ||
          typeof canvas.value.zoomToPoint !== 'function' ||
          typeof canvas.value.setCursor !== 'function') {
        console.warn('Canvas zoom/cursor methods not available')
        return false
      }

      return true
    } catch (error) {
      console.warn('Canvas readiness check failed:', error)
      return false
    }
  }

  const eventHandlers = {
    mouseDown: null as ((opt: any) => void) | null,
    mouseMove: null as ((opt: any) => void) | null,
    mouseUp: null as (() => void) | null,
    mouseWheel: null as ((opt: any) => void) | null
  }

  const enablePanning = () => {
    // 如果事件已经绑定，先解绑
    if (isEventsBound.value) {
      disablePanning()
    }

    if (!isCanvasReady()) {
      if (retryCount < maxRetries) {
        retryCount++
        console.warn(`Canvas not ready for event binding, retrying... (${retryCount}/${maxRetries})`)
        retryTimeout = setTimeout(() => enablePanning(), 300) as any
        return
      } else {
        console.error('Canvas failed to become ready after maximum retries')
        return
      }
    }

    try {
      // 确保 canvas.value 存在
      if (!canvas.value) {
        throw new Error('Canvas is null during event binding')
      }

      eventHandlers.mouseDown = (opt: any) => {
        if (!canvas.value) return
        const evt = opt.e as MouseEvent
        if (evt.altKey || evt.button === 1) {
          isDragging.value = true
          canvas.value.selection = false
          lastPanPoint.value.x = evt.clientX
          lastPanPoint.value.y = evt.clientY
          canvas.value.setCursor('grabbing')
        }
      }

      eventHandlers.mouseMove = (opt: any) => {
        if (isDragging.value && canvas.value) {
          const evt = opt.e as MouseEvent
          const vpt = canvas.value.viewportTransform
          if (vpt && Array.isArray(vpt) && vpt.length >= 6) {
            vpt[4] += evt.clientX - lastPanPoint.value.x
            vpt[5] += evt.clientY - lastPanPoint.value.y
            canvas.value.requestRenderAll()
            lastPanPoint.value.x = evt.clientX
            lastPanPoint.value.y = evt.clientY
          }
        }
      }

      eventHandlers.mouseUp = () => {
        if (canvas.value) {
          const vpt = canvas.value.viewportTransform
          if (vpt) {
            canvas.value.setViewportTransform(vpt)
          }
          isDragging.value = false
          canvas.value.selection = true
          canvas.value.setCursor('default')
        }
      }

      eventHandlers.mouseWheel = (opt: any) => {
        if (!canvas.value) return
        const evt = opt.e as WheelEvent
        const delta = evt.deltaY
        let zoom = canvas.value.getZoom()
        zoom *= 0.999 ** delta
        zoom = Math.max(0.1, Math.min(10, zoom))

        const point = new fabric.Point(evt.offsetX, evt.offsetY)
        canvas.value.zoomToPoint(point, zoom)

        if (onZoomChange) onZoomChange(zoom)
        evt.preventDefault()
        evt.stopPropagation()
      }

      // 绑定事件
      canvas.value.on('mouse:down', eventHandlers.mouseDown)
      canvas.value.on('mouse:move', eventHandlers.mouseMove)
      canvas.value.on('mouse:up', eventHandlers.mouseUp)
      canvas.value.on('mouse:wheel', eventHandlers.mouseWheel)

      isEventsBound.value = true
      retryCount = 0
      console.log('✅ Image navigation events bound successfully')
    } catch (error) {
      console.error('❌ Failed to bind image navigation events:', error)
      isEventsBound.value = false
      throw error
    }
  }

  const disablePanning = () => {
    if (!canvas.value) return
    if (retryTimeout) {
      clearTimeout(retryTimeout)
      retryTimeout = null
    }

    try {
      if (eventHandlers.mouseDown) {
        canvas.value.off('mouse:down', eventHandlers.mouseDown)
      }
      if (eventHandlers.mouseMove) {
        canvas.value.off('mouse:move', eventHandlers.mouseMove)
      }
      if (eventHandlers.mouseUp) {
        canvas.value.off('mouse:up', eventHandlers.mouseUp)
      }
      if (eventHandlers.mouseWheel) {
        canvas.value.off('mouse:wheel', eventHandlers.mouseWheel)
      }

      eventHandlers.mouseDown = null
      eventHandlers.mouseMove = null
      eventHandlers.mouseUp = null
      eventHandlers.mouseWheel = null
      isEventsBound.value = false

      console.log('✅ Image navigation events unbound successfully')
    } catch (error) {
      console.warn('Failed to remove some event listeners:', error)
    }
  }

  const resetViewport = () => {
    if (!canvas.value) return
    canvas.value.setViewportTransform([1, 0, 0, 1, 0, 0])
    canvas.value.setZoom(1)
    canvas.value.requestRenderAll()
  }

  const centerView = () => {
    if (!canvas.value) return
    const objects = canvas.value.getObjects()
    if (objects.length === 0) return
    const group = new fabric.Group(objects, { left: 0, top: 0 })
    const { width, height } = group
    const { width: canvasWidth, height: canvasHeight } = canvas.value
    canvas.value.setViewportTransform([1, 0, 0, 1, (canvasWidth - width!) / 2, (canvasHeight - height!) / 2])
    canvas.value.requestRenderAll()
  }

  const panTo = (x: number, y: number) => {
    if (!canvas.value) return
    const vpt = canvas.value.viewportTransform!
    vpt[4] = x
    vpt[5] = y
    canvas.value.setViewportTransform(vpt)
    canvas.value.requestRenderAll()
  }

  const getPanPosition = () => {
    if (!canvas.value) return { x: 0, y: 0 }
    const vpt = canvas.value.viewportTransform!
    return { x: vpt[4], y: vpt[5] }
  }

  const handleKeyNavigation = (event: KeyboardEvent) => {
    if (!canvas.value || !canvas.value.getActiveObject()) return
    const step = 10
    const vpt = canvas.value.viewportTransform!
    switch (event.key) {
      case 'ArrowUp': vpt[5] += step; break
      case 'ArrowDown': vpt[5] -= step; break
      case 'ArrowLeft': vpt[4] += step; break
      case 'ArrowRight': vpt[4] -= step; break
      default: return
    }
    event.preventDefault()
    canvas.value.setViewportTransform(vpt)
    canvas.value.requestRenderAll()
  }

  onUnmounted(() => {
    disablePanning()
    if (retryTimeout) {
      clearTimeout(retryTimeout)
      retryTimeout = null
    }
  })

  return {
    isDragging: readonly(isDragging),
    enablePanning,
    disablePanning,
    resetViewport,
    centerView,
    panTo,
    getPanPosition,
    handleKeyNavigation
  }
}