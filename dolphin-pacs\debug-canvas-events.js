// 调试 Canvas 事件绑定问题的脚本
// 在浏览器控制台中运行此脚本来诊断问题

console.log('🔍 开始诊断 Fabric.js Canvas 事件问题...');

// 检查 Fabric.js 是否已加载
if (typeof fabric === 'undefined') {
  console.error('❌ Fabric.js 未加载');
} else {
  console.log('✅ Fabric.js 已加载，版本:', fabric.version);
}

// 查找 Canvas 元素
const canvasElement = document.getElementById('ultrasound-canvas');
if (!canvasElement) {
  console.error('❌ 未找到 Canvas 元素 #ultrasound-canvas');
} else {
  console.log('✅ 找到 Canvas 元素:', canvasElement);
}

// 检查是否有现有的 Fabric Canvas 实例
let existingCanvas = null;
if (window.fabric && window.fabric.Canvas) {
  // 尝试从全局变量或 Vue 实例中获取 Canvas
  if (window.__VUE_DEVTOOLS_GLOBAL_HOOK__) {
    console.log('🔍 检查 Vue DevTools...');
  }
}

// 创建测试 Canvas 实例
function testCanvasCreation() {
  console.log('🧪 测试创建新的 Canvas 实例...');
  
  try {
    const testCanvas = new fabric.Canvas(canvasElement, {
      selection: false,
      evented: true
    });
    
    console.log('✅ Canvas 创建成功');
    console.log('Canvas 对象:', testCanvas);
    console.log('Canvas.on 方法:', typeof testCanvas.on);
    console.log('Canvas.off 方法:', typeof testCanvas.off);
    console.log('Canvas 元素:', testCanvas.getElement());
    
    // 测试事件绑定
    console.log('🧪 测试事件绑定...');
    
    const testHandler = (opt) => {
      console.log('✅ 测试事件触发成功');
    };
    
    try {
      testCanvas.on('mouse:down', testHandler);
      console.log('✅ 事件绑定成功');
      
      // 测试事件移除
      testCanvas.off('mouse:down', testHandler);
      console.log('✅ 事件移除成功');
      
    } catch (eventError) {
      console.error('❌ 事件绑定/移除失败:', eventError);
    }
    
    // 清理测试 Canvas
    testCanvas.dispose();
    console.log('✅ 测试 Canvas 已清理');
    
  } catch (error) {
    console.error('❌ Canvas 创建失败:', error);
  }
}

// 检查 DOM 状态
function checkDOMState() {
  console.log('🔍 检查 DOM 状态...');
  console.log('Document ready state:', document.readyState);
  console.log('Canvas 父元素:', canvasElement?.parentNode);
  console.log('Canvas 尺寸:', {
    width: canvasElement?.width,
    height: canvasElement?.height,
    clientWidth: canvasElement?.clientWidth,
    clientHeight: canvasElement?.clientHeight
  });
}

// 运行诊断
if (canvasElement) {
  checkDOMState();
  testCanvasCreation();
} else {
  console.log('⏳ 等待 Canvas 元素出现...');
  
  // 等待 Canvas 元素出现
  const observer = new MutationObserver((mutations) => {
    const canvas = document.getElementById('ultrasound-canvas');
    if (canvas) {
      console.log('✅ Canvas 元素已出现');
      observer.disconnect();
      checkDOMState();
      testCanvasCreation();
    }
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
  // 5秒后停止观察
  setTimeout(() => {
    observer.disconnect();
    console.log('⏰ 停止等待 Canvas 元素');
  }, 5000);
}

console.log('🔍 诊断脚本已运行完成');
