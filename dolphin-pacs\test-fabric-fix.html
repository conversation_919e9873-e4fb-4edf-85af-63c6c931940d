<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fabric.js 修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .canvas-container {
            border: 1px solid #ddd;
            margin: 20px 0;
            background: #f8f9fa;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Fabric.js 事件系统修复测试</h1>
        
        <div id="status" class="status warning">
            正在初始化...
        </div>
        
        <div class="canvas-container">
            <canvas id="test-canvas" width="600" height="400"></canvas>
        </div>
        
        <div>
            <button id="init-btn">初始化 Canvas</button>
            <button id="bind-events-btn" disabled>绑定事件</button>
            <button id="test-events-btn" disabled>测试事件</button>
            <button id="clear-btn" disabled>清除</button>
        </div>
        
        <div id="log" style="margin-top: 20px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/fabric@6.7.1/dist/fabric.min.js"></script>
    <script>
        let canvas = null;
        let eventHandlers = {
            mouseDown: null,
            mouseMove: null,
            mouseUp: null,
            mouseWheel: null
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : type === 'warning' ? 'orange' : 'black';
            logDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function updateButtons(init, bind, test, clear) {
            document.getElementById('init-btn').disabled = !init;
            document.getElementById('bind-events-btn').disabled = !bind;
            document.getElementById('test-events-btn').disabled = !test;
            document.getElementById('clear-btn').disabled = !clear;
        }

        async function initCanvas() {
            try {
                log('开始初始化 Fabric Canvas...');
                
                const canvasElement = document.getElementById('test-canvas');
                if (!canvasElement) {
                    throw new Error('Canvas 元素未找到');
                }

                canvas = new fabric.Canvas(canvasElement, {
                    selection: false,
                    preserveObjectStacking: true,
                    renderOnAddRemove: false,
                    skipTargetFind: true,
                    evented: true,
                    backgroundColor: '#f8f9fa'
                });

                // 等待初始化完成
                await new Promise(resolve => setTimeout(resolve, 100));

                // 检查事件系统
                if (typeof canvas.on !== 'function') {
                    throw new Error('Canvas 事件系统未初始化');
                }

                log('Canvas 初始化成功', 'success');
                updateStatus('Canvas 已初始化，可以绑定事件', 'success');
                updateButtons(false, true, false, true);

            } catch (error) {
                log(`初始化失败: ${error.message}`, 'error');
                updateStatus('初始化失败', 'error');
                updateButtons(true, false, false, false);
            }
        }

        function bindEvents() {
            if (!canvas) {
                log('Canvas 未初始化', 'error');
                return;
            }

            try {
                log('开始绑定事件处理器...');

                // 创建事件处理器
                eventHandlers.mouseDown = (opt) => {
                    log('鼠标按下事件触发', 'success');
                };

                eventHandlers.mouseMove = (opt) => {
                    // 不记录移动事件，太频繁
                };

                eventHandlers.mouseUp = () => {
                    log('鼠标抬起事件触发', 'success');
                };

                eventHandlers.mouseWheel = (opt) => {
                    log('鼠标滚轮事件触发', 'success');
                    opt.e.preventDefault();
                };

                // 绑定事件
                canvas.on('mouse:down', eventHandlers.mouseDown);
                canvas.on('mouse:move', eventHandlers.mouseMove);
                canvas.on('mouse:up', eventHandlers.mouseUp);
                canvas.on('mouse:wheel', eventHandlers.mouseWheel);

                log('事件绑定成功', 'success');
                updateStatus('事件已绑定，可以测试交互', 'success');
                updateButtons(false, false, true, true);

            } catch (error) {
                log(`事件绑定失败: ${error.message}`, 'error');
                updateStatus('事件绑定失败', 'error');
            }
        }

        function testEvents() {
            log('请在 Canvas 上进行以下操作来测试事件:', 'warning');
            log('1. 点击 Canvas', 'warning');
            log('2. 在 Canvas 上滚动鼠标滚轮', 'warning');
            log('3. 拖拽鼠标', 'warning');
        }

        function clearCanvas() {
            if (canvas) {
                try {
                    // 移除事件监听器
                    if (eventHandlers.mouseDown) {
                        canvas.off('mouse:down', eventHandlers.mouseDown);
                    }
                    if (eventHandlers.mouseMove) {
                        canvas.off('mouse:move', eventHandlers.mouseMove);
                    }
                    if (eventHandlers.mouseUp) {
                        canvas.off('mouse:up', eventHandlers.mouseUp);
                    }
                    if (eventHandlers.mouseWheel) {
                        canvas.off('mouse:wheel', eventHandlers.mouseWheel);
                    }

                    canvas.dispose();
                    canvas = null;
                    eventHandlers = { mouseDown: null, mouseMove: null, mouseUp: null, mouseWheel: null };

                    log('Canvas 已清除', 'success');
                    updateStatus('Canvas 已清除，可以重新初始化', 'warning');
                    updateButtons(true, false, false, false);

                } catch (error) {
                    log(`清除失败: ${error.message}`, 'error');
                }
            }
        }

        // 绑定按钮事件
        document.getElementById('init-btn').addEventListener('click', initCanvas);
        document.getElementById('bind-events-btn').addEventListener('click', bindEvents);
        document.getElementById('test-events-btn').addEventListener('click', testEvents);
        document.getElementById('clear-btn').addEventListener('click', clearCanvas);

        // 初始状态
        updateButtons(true, false, false, false);
        log('测试页面已加载，点击"初始化 Canvas"开始测试');
    </script>
</body>
</html>
